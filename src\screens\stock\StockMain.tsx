import React, {Component} from 'react';
import { WithTranslation, withTranslation } from 'react-i18next';
import {
    View,
    Text,
    TextInput,
    FlatList,
    Image,
    TouchableOpacity,
    StyleSheet,
    ListRenderItemInfo,
    Pressable
} from 'react-native';
import {moderateScale, verticalScale} from 'react-native-size-matters';
import { renderBold, renderRegular } from '../../services/FontProvider';


interface Appointment {
    id : number;
    title : string;
    description : string;
    page:string;
    backgroundColor : string;
    titleColor : string;
}

interface State {
    searchQuery : string;
    appointments : Appointment[];
}
interface StockMainProps extends WithTranslation{}

class StockMain extends Component < StockMainProps, State > {
    constructor(props) {
        super(props);
        this.state = {
            searchQuery: '',
            appointments: [
                {
                    id: 1,
                    title: 'Project 1 Large name',
                    description: 'StockMain.daily',
                    page:"",
                    backgroundColor: '#ffdcb2',
                    titleColor: '#ff8c00'
                }, {
                    id: 2,
                    title: 'Project 2',
                    description: 'StockMain.income',
                    page:"",
                    backgroundColor: '#bfdfdf',
                    titleColor: '#008080'
                }, {
                    id: 3,
                    title: 'Project 2',
                    description: 'StockMain.topSales',
                    page:"",
                    backgroundColor: '#e2caf8',
                    titleColor: '#8a2be2'
                }
            ]
        };
    }

    searchFilter = (item : Appointment): boolean => {
        const query = this
            .state
            .searchQuery
            .toLowerCase();
        return item
            .title
            .toLowerCase()
            .includes(query);
    };

    renderAppointmentCard = ({item} : ListRenderItemInfo < Appointment >) => {
      const {t} = this.props;
      return(
        <Pressable
            style={[
            styles.card, {
                backgroundColor: item.backgroundColor,
                borderTopWidth: moderateScale(4),
                borderTopColor: item.titleColor
            }
        ]}>
            <Text
                style={[
                styles.cardTitle, {
                    color: item.titleColor
                }
            ]}>{item.title}</Text>
            <View style={styles.cardDates}>
                <Text style={styles.cardDate}>{t(item.description)}</Text>
            </View>
        </Pressable>
        )
    };

    render() {
        const {searchQuery, appointments} = this.state;
        const filteredAppointments = appointments.filter(this.searchFilter);

        return (
            <View style={styles.container}>
                <FlatList
                    contentContainerStyle={styles.listContainer}
                    data={filteredAppointments}
                    showsVerticalScrollIndicator={false}
                    renderItem={this.renderAppointmentCard}
                    keyExtractor={(item) => item.id.toString()}
                    numColumns={2}/>
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: moderateScale(10),
    },
    listContainer: {
        paddingHorizontal: moderateScale(5)
    },
    title: {
        fontSize: moderateScale(20),
        fontWeight: 'bold',
        marginBottom: verticalScale(10)
    },
    searchInput: {
        height: verticalScale(40),
        borderWidth: moderateScale(2),
        borderRadius: moderateScale(5),
        borderColor: '#A9A9A9',
        marginBottom: verticalScale(10),
        paddingHorizontal: moderateScale(10)
    },
    card: {
        flex: 1,
        marginBottom: verticalScale(20),
        padding: moderateScale(10),
        borderRadius: moderateScale(8),
        marginHorizontal: moderateScale(10),

        shadowColor: '#000',
        shadowOpacity: 0.2,
        shadowOffset: {
            width: 0,
            height: verticalScale(2)
        },
        shadowRadius: moderateScale(4)
    },
    cardTitle: {
        fontSize: moderateScale(18),
        fontFamily: renderBold(),
        paddingVertical: verticalScale(5),
        textAlign:'center',
    },
    cardDates: {
        flexDirection: 'row',
        paddingVertical: verticalScale(5)
    },
    cardDate: {
        color: '#888',
        fontSize: moderateScale(16),
        fontFamily: renderRegular(),
        textAlign:'center',
        alignContent:"center",
        backgroundColor:'purple'
    },
    cardContent: {
        justifyContent: 'space-between',
        paddingTop: verticalScale(10)
    },
    attendeesContainer: {
        flexWrap: 'wrap',
        flexDirection: 'row',
        paddingHorizontal: moderateScale(10)
    },
    attendeeImage: {
        width: moderateScale(30),
        height: moderateScale(30),
        borderRadius: moderateScale(20),
        marginLeft: moderateScale(-10),
        borderWidth: 0.5,
        marginTop: verticalScale(3)
    },
    buttonsContainer: {
        flexDirection: 'row',
        justifyContent:'center',
        width:"100%"
    }
});

export default withTranslation()(StockMain);