import React, {Component} from 'react';
import { WithTranslation, withTranslation } from 'react-i18next';
import {
    View,
    Text,
    TextInput,
    FlatList,
    Image,
    TouchableOpacity,
    StyleSheet,
    ListRenderItemInfo,
    Pressable
} from 'react-native';
import {moderateScale, verticalScale} from 'react-native-size-matters';
import { renderBold, renderRegular } from '../../services/FontProvider';


interface StockSection {
    id: number;
    name: string;
    description: string;
    page: string;
    backgroundColor: string;
    titleColor: string;
}

interface State {
    searchQuery: string;
    stockSections: StockSection[];
}
interface StockMainProps extends WithTranslation{}

class StockMain extends Component < StockMainProps, State > {
    constructor(props) {
        super(props);
        this.state = {
            searchQuery: '',
            stockSections: [
                {
                    id: 1,
                    name: 'stockMain.leftoversTitle',
                    description: 'stockMain.leftoversDescription',
                    page: 'Leftovers',
                    backgroundColor: '#ffdcb2',
                    titleColor: '#cc6600'
                }, {
                    id: 2,
                    name: 'stockMain.criticalLeftovers',
                    description: 'stockMain.criticalLeftoversDescription',
                    page: 'LowStock',
                    backgroundColor: '#bfdfdf',
                    titleColor: '#006666'
                }, {
                    id: 3,
                    name: 'stockMain.expirationDates',
                    description: 'stockMain.expirationDatesDescription',
                    page: 'ExpirationDate',
                    backgroundColor: '#e2caf8',
                    titleColor: '#6b1f96'
                }
            ]
        };
    }

    renderStockSectionCard = ({item}: ListRenderItemInfo<StockSection>) => {
        const { t } = this.props;
        return (
            <TouchableOpacity
                style={[
                    styles.card, {
                        backgroundColor: item.backgroundColor,
                        borderTopWidth: moderateScale(4),
                        borderTopColor: item.titleColor
                    }
                ]}
                onPress={() => this.props.navigation.navigate(item.page)}
            >
                <Text
                    style={[
                        styles.cardTitle, {
                            color: item.titleColor
                        }
                    ]}
                >
                    {t(item.name)}
                </Text>
                <View style={styles.cardDescription}>
                    <Text style={styles.cardDescriptionText}>
                        {t(item.description)}
                    </Text>
                </View>
            </TouchableOpacity>
        );
    };

    render() {
        const {stockSections} = this.state;

        return (
            <View style={styles.container}>
                <FlatList
                    contentContainerStyle={styles.listContainer}
                    data={stockSections}
                    showsVerticalScrollIndicator={false}
                    renderItem={this.renderStockSectionCard}
                    keyExtractor={(item) => item.id.toString()}
                    numColumns={2}
                />
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: moderateScale(10),
    },
    listContainer: {
        paddingHorizontal: moderateScale(5)
    },
    title: {
        fontSize: moderateScale(20),
        fontWeight: 'bold',
        marginBottom: verticalScale(10)
    },
    searchInput: {
        height: verticalScale(40),
        borderWidth: moderateScale(2),
        borderRadius: moderateScale(5),
        borderColor: '#A9A9A9',
        marginBottom: verticalScale(10),
        paddingHorizontal: moderateScale(10)
    },
    card: {
        flex: 1,
        marginBottom: verticalScale(20),
        padding: moderateScale(15),
        borderRadius: moderateScale(8),
        marginHorizontal: moderateScale(10),
        minHeight: verticalScale(120),
        justifyContent: 'center',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOpacity: 0.2,
        shadowOffset: {
            width: 0,
            height: verticalScale(2)
        },
        shadowRadius: moderateScale(4),
        elevation: 3,
    },
    cardTitle: {
        fontSize: moderateScale(18),
        fontFamily: renderBold(),
        paddingVertical: verticalScale(8),
        textAlign: 'center',
        fontWeight: 'bold',
    },
    cardDescription: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: verticalScale(5),
    },
    cardDescriptionText: {
        color: '#555',
        fontSize: moderateScale(14),
        fontFamily: renderRegular(),
        textAlign: 'center',
        lineHeight: moderateScale(20),
        paddingHorizontal: moderateScale(8),
    },
    cardContent: {
        justifyContent: 'space-between',
        paddingTop: verticalScale(10)
    },
    attendeesContainer: {
        flexWrap: 'wrap',
        flexDirection: 'row',
        paddingHorizontal: moderateScale(10)
    },
    attendeeImage: {
        width: moderateScale(30),
        height: moderateScale(30),
        borderRadius: moderateScale(20),
        marginLeft: moderateScale(-10),
        borderWidth: 0.5,
        marginTop: verticalScale(3)
    },
    buttonsContainer: {
        flexDirection: 'row',
        justifyContent:'center',
        width:"100%"
    }
});

export default withTranslation()(StockMain);