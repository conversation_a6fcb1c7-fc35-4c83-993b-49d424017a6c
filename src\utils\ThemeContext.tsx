import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface ThemeColors {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
    card: string;
    white: string;
    black: string;
    yellow: string;
    redOrange: string;
    red: string;
    darkGray: string;
    lightGray: string;
    gray: string;
    lightGreen: string;
    success: string;
    warning: string;
    error: string;
    info: string;
    shadow: string;
    overlay: string;
}

export const lightTheme: ThemeColors = {
    primary: '#00C569',
    secondary: '#fff',
    background: '#ffffff',
    surface: '#f8f9fa',
    text: '#000000',
    textSecondary: '#666666',
    border: '#e0e0e0',
    card: '#ffffff',
    white: '#ffffff',
    black: '#000000',
    yellow: '#FFC107',
    redOrange: '#FF3D00',
    red: '#E80057',
    darkGray: '#929292',
    lightGray: '#DDDDDD',
    gray: '#BEBEBE',
    lightGreen: 'rgba(0,197,105, 0.2)',
    success: '#00C569',
    warning: '#FFC107',
    error: '#E80057',
    info: '#2196F3',
    shadow: '#000000',
    overlay: 'rgba(0, 0, 0, 0.5)',
};

export const darkTheme: ThemeColors = {
    primary: '#00C569',
    secondary: '#1e1e1e',
    background: '#121212',
    surface: '#1e1e1e',
    text: '#ffffff',
    textSecondary: '#b3b3b3',
    border: '#333333',
    card: '#1e1e1e',
    white: '#ffffff',
    black: '#000000',
    yellow: '#FFD54F',
    redOrange: '#FF5722',
    red: '#F44336',
    darkGray: '#757575',
    lightGray: '#424242',
    gray: '#616161',
    lightGreen: 'rgba(0,197,105, 0.3)',
    success: '#4CAF50',
    warning: '#FF9800',
    error: '#F44336',
    info: '#2196F3',
    shadow: '#000000',
    overlay: 'rgba(0, 0, 0, 0.7)',
};

export type ThemeMode = 'light' | 'dark';

interface ThemeContextType {
    theme: ThemeColors;
    themeMode: ThemeMode;
    toggleTheme: () => void;
    setTheme: (mode: ThemeMode) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
    children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
    const [themeMode, setThemeMode] = useState<ThemeMode>('light');

    useEffect(() => {
        loadTheme();
    }, []);

    const loadTheme = async () => {
        try {
            const savedTheme = await AsyncStorage.getItem('theme');
            if (savedTheme && (savedTheme === 'light' || savedTheme === 'dark')) {
                setThemeMode(savedTheme as ThemeMode);
            }
        } catch (error) {
            console.error('Error loading theme:', error);
        }
    };

    const saveTheme = async (mode: ThemeMode) => {
        try {
            await AsyncStorage.setItem('theme', mode);
        } catch (error) {
            console.error('Error saving theme:', error);
        }
    };

    const toggleTheme = () => {
        const newMode = themeMode === 'light' ? 'dark' : 'light';
        setThemeMode(newMode);
        saveTheme(newMode);
    };

    const setTheme = (mode: ThemeMode) => {
        setThemeMode(mode);
        saveTheme(mode);
    };

    const theme = themeMode === 'light' ? lightTheme : darkTheme;

    return (
        <ThemeContext.Provider value={{ theme, themeMode, toggleTheme, setTheme }}>
            {children}
        </ThemeContext.Provider>
    );
};

export const useTheme = (): ThemeContextType => {
    const context = useContext(ThemeContext);
    if (context === undefined) {
        throw new Error('useTheme must be used within a ThemeProvider');
    }
    return context;
};

// Legacy support - function to get current theme colors
export const getThemeColors = (mode: ThemeMode = 'light'): ThemeColors => {
    return mode === 'light' ? lightTheme : darkTheme;
};
