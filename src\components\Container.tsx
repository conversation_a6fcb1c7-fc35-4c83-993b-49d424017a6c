import React, {ReactNode} from 'react';
import {ScrollView, StyleSheet, View, SafeAreaView, ViewStyle} from 'react-native';
import {scale} from 'react-native-size-matters';
import { appColors } from '../utils/appColors';

interface ContainerProps {
    children : ReactNode;
    isScrollable?: boolean;
    bodyStyle?: ViewStyle;
}

class Container extends React.Component < ContainerProps > {
    renderContent() {
        const {children, isScrollable, bodyStyle} = this.props;

        if (isScrollable) {
            return (
                <ScrollView showsVerticalScrollIndicator={false}>
                    <View style={[styles.innerView, bodyStyle]}>
                        {children}
                    </View>
                </ScrollView>
            );
        }

        return <View style={[styles.innerView, bodyStyle]}>{children}</View>;
    }

    render() {
        return <SafeAreaView style={styles.container}>{this.renderContent()}</SafeAreaView>;
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        //backgroundColor:appColors.white
    },
    innerView: {
        flex: 1,
        paddingHorizontal: scale(10)
    }
});

export default Container;
