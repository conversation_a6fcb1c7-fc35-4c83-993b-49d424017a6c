import * as SQLite from 'expo-sqlite';

interface Agent {
    id: number;
    name: string;
    address: string;
    phone: string;
    typeOfBusiness: string;
    iconId: number;
}

interface CreateAgentData {
    name: string;
    address: string;
    phone: string;
    typeOfBusiness: string;
    iconId: number;
}

interface UpdateAgentData {
    name?: string;
    address?: string;
    phone?: string;
    typeOfBusiness?: string;
    iconId?: number;
}

class AgentCRUD {
    private db: SQLite.SQLiteDatabase | null = null;

    constructor() {
        this.initDB();
    }

    async initDB(): Promise<void> {
        this.db = await SQLite.openDatabaseAsync('Reports.db');
    }

    // Create: Insert a new agent
    async createAgent(agentData: CreateAgentData): Promise<number> {
        if (!this.db) {
            throw new Error('Database not initialized');
        }
        try {
            const result = await this.db.runAsync(
                `INSERT INTO Agents (name, address, phone, typeOfBusiness, iconId) VALUES (?, ?, ?, ?, ?);`,
                [agentData.name, agentData.address, agentData.phone, agentData.typeOfBusiness, agentData.iconId]
            );
            return result.lastInsertRowId;
        } catch (error) {
            throw error;
        }
    }

    // Read: Get all agents
    async getAllAgents(): Promise<Agent[]> {
        if (!this.db) {
            throw new Error('Database not initialized');
        }
        try {
            const result = await this.db.getAllAsync(`SELECT * FROM Agents ORDER BY name ASC;`);
            return result as Agent[];
        } catch (error) {
            throw error;
        }
    }

    // Read: Get agent by ID
    async getAgentById(id: number): Promise<Agent | null> {
        if (!this.db) {
            throw new Error('Database not initialized');
        }
        try {
            const results = await this.db.getAllAsync(
                `SELECT * FROM Agents WHERE id = ?;`,
                [id]
            );
            return results.length > 0 ? results[0] as Agent : null;
        } catch (error) {
            throw error;
        }
    }

    // Read: Get agents by type of business
    async getAgentsByType(typeOfBusiness: string): Promise<Agent[]> {
        if (!this.db) {
            throw new Error('Database not initialized');
        }
        try {
            const results = await this.db.getAllAsync(
                `SELECT * FROM Agents WHERE typeOfBusiness = ? ORDER BY name ASC;`,
                [typeOfBusiness]
            );
            return results as Agent[];
        } catch (error) {
            throw error;
        }
    }

    // Read: Search agents by name
    async searchAgentsByName(searchTerm: string): Promise<Agent[]> {
        if (!this.db) {
            throw new Error('Database not initialized');
        }
        try {
            const results = await this.db.getAllAsync(
                `SELECT * FROM Agents WHERE name LIKE ? ORDER BY name ASC;`,
                [`%${searchTerm}%`]
            );
            return results as Agent[];
        } catch (error) {
            throw error;
        }
    }

    // Update: Update agent by ID
    async updateAgent(id: number, updateData: UpdateAgentData): Promise<boolean> {
        if (!this.db) {
            throw new Error('Database not initialized');
        }
        
        const updateFields: string[] = [];
        const updateValues: any[] = [];
        
        if (updateData.name !== undefined) {
            updateFields.push('name = ?');
            updateValues.push(updateData.name);
        }
        if (updateData.address !== undefined) {
            updateFields.push('address = ?');
            updateValues.push(updateData.address);
        }
        if (updateData.phone !== undefined) {
            updateFields.push('phone = ?');
            updateValues.push(updateData.phone);
        }
        if (updateData.typeOfBusiness !== undefined) {
            updateFields.push('typeOfBusiness = ?');
            updateValues.push(updateData.typeOfBusiness);
        }
        if (updateData.iconId !== undefined) {
            updateFields.push('iconId = ?');
            updateValues.push(updateData.iconId);
        }
        
        if (updateFields.length === 0) {
            throw new Error('No fields to update');
        }
        
        updateValues.push(id);
        
        try {
            const result = await this.db.runAsync(
                `UPDATE Agents SET ${updateFields.join(', ')} WHERE id = ?;`,
                updateValues
            );
            return result.changes > 0;
        } catch (error) {
            throw error;
        }
    }

    // Delete: Delete agent by ID
    async deleteAgent(id: number): Promise<boolean> {
        if (!this.db) {
            throw new Error('Database not initialized');
        }
        try {
            const result = await this.db.runAsync(
                `DELETE FROM Agents WHERE id = ?;`,
                [id]
            );
            return result.changes > 0;
        } catch (error) {
            throw error;
        }
    }

    // Utility: Get agent count
    async getAgentCount(): Promise<number> {
        if (!this.db) {
            throw new Error('Database not initialized');
        }
        try {
            const result = await this.db.getFirstAsync(`SELECT COUNT(*) as count FROM Agents;`) as { count: number };
            return result.count;
        } catch (error) {
            throw error;
        }
    }

    // Utility: Check if agent exists by name and phone (to prevent duplicates)
    async agentExists(name: string, phone: string): Promise<boolean> {
        if (!this.db) {
            throw new Error('Database not initialized');
        }
        try {
            const result = await this.db.getFirstAsync(
                `SELECT COUNT(*) as count FROM Agents WHERE name = ? AND phone = ?;`,
                [name, phone]
            ) as { count: number };
            return result.count > 0;
        } catch (error) {
            throw error;
        }
    }
}

export default new AgentCRUD();
export type { Agent, CreateAgentData, UpdateAgentData };
