import React, {Component} from 'react';
import {View, Text, TouchableOpacity, StyleSheet, ColorValue} from 'react-native';
import {Button, TextInput, IconButton, ActivityIndicator} from '@react-native-material/core';
import {scale, verticalScale, moderateScale} from 'react-native-size-matters';
import Container from '../../components/Container';
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import {appColors} from '../../utils/appColors';
import  Animated, {  FadeInUp }  from 'react-native-reanimated';
import {AntDesign, Entypo, Ionicons, MaterialIcons} from '@expo/vector-icons';
//import { LoginDTO } from '../../models/User';
//import AuthService from '../../services/AuthService';
import Toast, {Positions} from 'react-native-root-toast';
//import LocalUserService from '../../services/memory/LocalUserService';
import {withTranslation, WithTranslation} from 'react-i18next';
import { renderBold, renderRegular } from '../../services/FontProvider';
import UserService from '../../services/backendCalls/UserService';
import UserCRUD from '../../services/sql/models/UserCRUD';

interface UserInputValidation {
    nameEmpty: boolean;
    emailEmpty: boolean;
    emailValidationFailed: boolean;
    passwordEmpty: boolean;
}

interface RegisterProps extends WithTranslation{
    navigation : any; 
}

interface LoginDTO{
    name: string;
    email:string;
    password:string;
}

interface LoginState {
    validation:UserInputValidation;
    loginDTO:LoginDTO;
    sendingRequest: boolean;
}

class Register extends Component < RegisterProps, LoginState > {
    constructor(props : RegisterProps) {
        super(props);
        this.state = {
            validation:{
                nameEmpty: false,
                emailEmpty: false,
                emailValidationFailed: false,
                passwordEmpty: false,
            },
            loginDTO:{
                email:'',
                password:'',
                name:''
            },
            sendingRequest: false
        };
    }

    onChangeName = (text : string) => {
        this.setState((prevState) => ({
            loginDTO: {
                ...prevState.loginDTO,
                name: text
            },
            validation:{
                ...prevState.validation,
                nameEmpty: false,
            }
        }));
    };

    onChangeEmail = ( text : string) => {
        this.setState((prevState) => ({
            loginDTO: {
                ...prevState.loginDTO,
                email: text
            },
            validation:{
                ...prevState.validation,
                emailEmpty: false,
                emailValidationFailed: false,
            }
        }));
    };

    onChangePass = (text : string) => {
        this.setState((prevState) => ({
            loginDTO: {
                ...prevState.loginDTO,
                password: text
            },
            validation:{
                ...prevState.validation,
                passwordEmpty: false,
            }
        }));
    };

    validateInput = () => {
        let { validation } = this.state;
        const { name, email, password } = this.state.loginDTO;
        let validationPassed = true;
        if (!name) {
            validation.nameEmpty = true;
            this.setState({validation : validation})
            validationPassed = false;
        }
        if (!email) {
            validation.emailEmpty = true;
            this.setState({validation : validation})
            validationPassed = false;
        }

        if (!password) {
            validation.passwordEmpty = true;
            this.setState({validation : validation})
            validationPassed = false;
        }   

        if(!this.validateEmail(email)){
            validation.emailValidationFailed = true;
            this.setState({validation : validation})
            validationPassed = false;
        }
    
        return validationPassed;
    }

    onSignUp = () => {
        
        if (this.validateInput()) {
            let { loginDTO } = this.state;
            
            this.setState({sendingRequest:true});
            this.onSignUpBackend(loginDTO);
            this.onSignUpLocal(loginDTO);
        }        
    };

    onSignUpLocal = (loginDTO:LoginDTO) => {
        //LocalUserService.postNewUser(loginDTO);
        UserCRUD.createUser(loginDTO.name, loginDTO.email, loginDTO.password);
    }

    onSignUpBackend = (loginDTO:LoginDTO) => {
        UserService.register(loginDTO)
                .then((user) =>{
                    let toast = Toast.show('Register success', {
                        duration: Toast.durations.SHORT,
                        position: Toast.positions.BOTTOM,
                        backgroundColor: appColors.primary,
                        shadow: true,
                        animation: true,
                        hideOnPress: true,
                        delay: 0,
                    });
                    setTimeout(function () {
                        Toast.hide(toast);
                    }, 5000);
                    this.setState({sendingRequest:false});
                    this.props.navigation.navigate("MyTabsNew");
                })
                .catch((err) => {
                    let toast = Toast.show('Login failed', {
                        duration: Toast.durations.SHORT,
                        position: Toast.positions.BOTTOM,
                        backgroundColor: appColors.red,
                        shadow: true,
                        animation: true,
                        hideOnPress: true,
                        delay: 0,
                    });
                    setTimeout(function () {
                        Toast.hide(toast);                        
                    }, 5000);
                    this.setState({sendingRequest:false});
        });
    }

    validateEmail = (email: string): boolean => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    render() {
        let { sendingRequest,} = this.state;
        const { nameEmpty, emailEmpty, emailValidationFailed, passwordEmpty } = this.state.validation;
        const {t} = this.props;
        return (
            <Container isScrollable>
                <KeyboardAwareScrollView
                    contentContainerStyle={styles.container}
                    extraScrollHeight={scale(100)}
                    enableOnAndroid={true}
        	    >
                {
                    sendingRequest
                    ?
                        <ActivityIndicator size={'large'} color={appColors.primary} />
                    :
                        <Animated.View
                            style={{
                            marginTop: verticalScale(100),
                            backgroundColor: appColors.white,
                            padding: scale(15),
                            borderRadius: scale(5)
                            }}
                            entering={FadeInUp.delay(300).duration(700)}
                        >                       
                        <View
                            style={{
                            paddingVertical: verticalScale(10)
                        }}>
                          
                        <TextInput
                            onChangeText={(text) => this.onChangeName(text)}
                            keyboardType='default'
                            leading={props => <Entypo name="user" {...props} />}
                            inputStyle={ nameEmpty ? {color: appColors.red, paddingTop:verticalScale(10)} :{ color: appColors.black, paddingTop:verticalScale(10)} }
                            color={appColors.black}
                            placeholder={t('register.name')}/>
                            {
                                nameEmpty
                                ?
                                <Text style={styles.errorMessage}>{t('register.nameError')}</Text>
                                :
                                null
                            } 
                        </View>
                        <View
                            style={{
                            paddingVertical: verticalScale(10)
                        }}>
                        <TextInput
                            onChangeText={(text) => this.onChangeEmail(text)}
                            keyboardType="email-address"
                            leading={props => <Entypo name="email" {...props} />}
                            inputStyle={ emailValidationFailed ? {color: appColors.red, paddingTop:verticalScale(10)} :{ color: appColors.black, paddingTop:verticalScale(10)} }
                            color={appColors.black}
                            placeholder="<EMAIL>"/>
                            {
                                emailEmpty
                                ?
                                <Text style={styles.errorMessage}>{t('register.emailError')}</Text>
                                :
                                emailValidationFailed
                                ?
                                <Text style={styles.errorMessage}>{t('register.emailInvalidError')}</Text>
                                :
                                null
                            } 
                            </View>  
                        <View
                            style={{
                            paddingVertical: verticalScale(10)
                        }}>
                            <TextInput
                                leading={props => <MaterialIcons name="password" {...props} />}
                                onChangeText={(text) => this.onChangePass(text)}
                                secureTextEntry
                                inputStyle={{paddingTop:verticalScale(10)}}
                                color={appColors.black}
                                placeholder={t('register.password')}/>
                            {
                                passwordEmpty
                                ?
                                <Text style={styles.errorMessage}>{t('register.passwordError')}</Text>
                                :
                                null
                            }
                        </View>
                        <View style={styles.socialLoginWrapper}>
                            <View>
                                <TouchableOpacity style={styles.button} onPress={() => this.onSignUp()}>                                
                                    <Text style={styles.btnTxt}>{t('register.login')}</Text>
                                    <AntDesign name='login' size={scale(20)} color={appColors.primary}/>
                                </TouchableOpacity>
                            </View>
                        </View>
                        <View style={styles.divider}/>
                        <View style={styles.hasAccount}>
                            <Text style={styles.loginTxt}>{t('register.hasAccount')} {" "}</Text>
                            <TouchableOpacity onPress={() => this.props.navigation.navigate("Register")}>
                                <Text style={styles.loginTxtSpan}>{t('register.login')}</Text>
                            </TouchableOpacity>                    
                        </View>
                        
                    </Animated.View>
                }
                </KeyboardAwareScrollView>
            </Container>
         
        );
    }
}

export default withTranslation()(Register);

const styles = StyleSheet.create({
        container: {
        flexGrow: 1,
        justifyContent: "center",
        //padding: 20,
    },
    socialLoginWrapper:{
        alignSelf:'stretch',
    },
    button:{
        flexDirection:'row',
        padding:scale(10),
        borderColor:appColors.gray,
        borderRadius:scale(25),
        borderWidth:StyleSheet.hairlineWidth,
        // width: '100%'
        alignItems:'center',
        justifyContent:'center',
        gap:scale(5),
        marginBottom:verticalScale(15),
    },
    btnTxt:{
        fontSize:moderateScale(16),
        fontFamily:renderBold(),
        color:appColors.black,
    },
    loginTxt:{
        fontSize: scale(15),
        color: appColors.black,
        lineHeight: 24,
        fontFamily:renderRegular(),
    },
    hasAccount:{
        flexDirection:'row',
        justifyContent:'center',
        marginBottom: verticalScale(30),
    },
    loginTxtSpan:{
        color:appColors.primary,
        fontFamily:renderBold(),
        lineHeight: 24,
        fontSize:scale(18),
    },
    divider:{
        flexDirection:'row',
        justifyContent:'center',
        borderTopColor:appColors.darkGray,
        borderTopWidth:StyleSheet.hairlineWidth,
        width:'100%',
        marginVertical:verticalScale(15),
    },
    errorMessage:{
        color:appColors.redOrange,
        fontSize:moderateScale(12),
        marginTop:verticalScale(5),
    }
})
