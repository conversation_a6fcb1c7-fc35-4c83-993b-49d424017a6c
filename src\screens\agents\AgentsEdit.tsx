import React, {Component} from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {TextInput, ActivityIndicator} from '@react-native-material/core';
import {scale} from 'react-native-size-matters';
import Container from '../../components/Container';
import {appColors} from '../../utils/appColors';
import Animated, {FadeInUp} from 'react-native-reanimated';
import {Entypo, Foundation, MaterialCommunityIcons, MaterialIcons} from '@expo/vector-icons';
import {withTranslation, WithTranslation} from 'react-i18next';
import Toast from 'react-native-root-toast';
import { possibleIcons } from './possibleIcons';
import AgentCRUD, { Agent, UpdateAgentData } from '../../services/sql/models/AgentsCRUD';

interface AgentInputValidation {
    nameEmpty: boolean;
    addressEmpty: boolean;
    phoneEmpty:boolean;
    typeOfBusinessEmpty:boolean;
}

interface AgentEditForm{
    name:string;
    address:string;
    phone:string;
    typeOfBusiness:string;
    selectedId:number | null;
}

interface AgentsEditProps extends WithTranslation {
    navigation: any;
    route: any;
}

interface AgentsEditState {
    validation: AgentInputValidation;
    editAgentForm: AgentEditForm;
    sendingRequest: boolean;
    loading: boolean;
    agent: Agent | null;
}

class AgentsEdit extends Component<AgentsEditProps, AgentsEditState> {
    constructor(props: AgentsEditProps) {
        super(props);
        this.state = {
            validation: {
                nameEmpty: false,
                addressEmpty: false,
                phoneEmpty: false,
                typeOfBusinessEmpty: false,
            },
            editAgentForm: {
                name: '',
                address: '',
                phone: '',
                typeOfBusiness: '',
                selectedId: 1
            },
            sendingRequest: false,
            loading: true,
            agent: null
        };
    }

    componentDidMount() {
        this.loadAgent();
    }

    loadAgent = async () => {
        const { route } = this.props;
        const agentId = route.params?.agentId;

        if (!agentId) {
            Toast.show('Agent ID not provided', {
                duration: Toast.durations.SHORT,
                position: Toast.positions.BOTTOM,
                backgroundColor: appColors.red,
            });
            this.props.navigation.goBack();
            return;
        }

        try {
            const agent = await AgentCRUD.getAgentById(agentId);
            if (agent) {
                this.setState({
                    agent,
                    editAgentForm: {
                        name: agent.name,
                        address: agent.address,
                        phone: agent.phone,
                        typeOfBusiness: agent.typeOfBusiness,
                        selectedId: agent.iconId
                    },
                    loading: false
                });
            } else {
                Toast.show('Agent not found', {
                    duration: Toast.durations.SHORT,
                    position: Toast.positions.BOTTOM,
                    backgroundColor: appColors.red,
                });
                this.props.navigation.goBack();
            }
        } catch (error) {
            console.error('Error loading agent:', error);
            Toast.show('Error loading agent', {
                duration: Toast.durations.SHORT,
                position: Toast.positions.BOTTOM,
                backgroundColor: appColors.red,
            });
            this.props.navigation.goBack();
        }
    };

    onChangeName = (text: string) => {
        this.setState({
            editAgentForm: { ...this.state.editAgentForm, name: text },
            validation: { ...this.state.validation, nameEmpty: false }
        });
    };

    onChangeAddress = (text: string) => {
        this.setState({
            editAgentForm: { ...this.state.editAgentForm, address: text },
            validation: { ...this.state.validation, addressEmpty: false }
        });
    };

    onChangePhone = (text: string) => {
        this.setState({
            editAgentForm: { ...this.state.editAgentForm, phone: text },
            validation: { ...this.state.validation, phoneEmpty: false }
        });
    };

    onChangeTypeOfBusiness = (text: string) => {
        this.setState({
            editAgentForm: { ...this.state.editAgentForm, typeOfBusiness: text },
            validation: { ...this.state.validation, typeOfBusinessEmpty: false }
        });
    };

    handleIconPress = (id: number) => {
        this.setState({
            editAgentForm: { ...this.state.editAgentForm, selectedId: id }
        });
    };

    validateInput = () => {
        let { validation } = this.state;
        const { name, address, phone, typeOfBusiness } = this.state.editAgentForm;
        let validationPassed = true;
        
        // Reset validation state
        validation = {
            nameEmpty: false,
            addressEmpty: false,
            phoneEmpty: false,
            typeOfBusinessEmpty: false,
        };

        if (!name.trim()) {
            validation.nameEmpty = true;
            validationPassed = false;
        }

        if (!address.trim()) {
            validation.addressEmpty = true;
            validationPassed = false;
        }   

        if (!phone.trim()) {
            validation.phoneEmpty = true;
            validationPassed = false;
        }

        if (!typeOfBusiness.trim()) {
            validation.typeOfBusinessEmpty = true;
            validationPassed = false;
        }   

        this.setState({ validation });
        return validationPassed;
    };

    onUpdate = async () => {
        if (this.validateInput()) {
            const { editAgentForm, agent } = this.state;
            const { t } = this.props;
            
            if (!agent) {
                Toast.show('Agent data not available', {
                    duration: Toast.durations.SHORT,
                    position: Toast.positions.BOTTOM,
                    backgroundColor: appColors.red,
                });
                return;
            }

            // Check if selectedId is valid
            if (!editAgentForm.selectedId) {
                Toast.show(t('agents.selectIconError') || 'Please select an icon', {
                    duration: Toast.durations.SHORT,
                    position: Toast.positions.BOTTOM,
                    backgroundColor: appColors.red,
                });
                return;
            }

            this.setState({ sendingRequest: true });

            try {
                // Update agent
                const updateData: UpdateAgentData = {
                    name: editAgentForm.name,
                    address: editAgentForm.address,
                    phone: editAgentForm.phone,
                    typeOfBusiness: editAgentForm.typeOfBusiness,
                    iconId: editAgentForm.selectedId
                };

                const success = await AgentCRUD.updateAgent(agent.id, updateData);

                if (success) {
                    // Show success message
                    Toast.show(t('agents.updateSuccess') || 'Agent updated successfully', {
                        duration: Toast.durations.SHORT,
                        position: Toast.positions.BOTTOM,
                        backgroundColor: appColors.primary,
                    });

                    // Navigate back
                    this.props.navigation.goBack();
                } else {
                    Toast.show(t('agents.updateError') || 'Failed to update agent', {
                        duration: Toast.durations.SHORT,
                        position: Toast.positions.BOTTOM,
                        backgroundColor: appColors.red,
                    });
                }

                this.setState({ sendingRequest: false });

            } catch (error) {
                console.error('Error updating agent:', error);
                Toast.show(t('agents.updateError') || 'Error updating agent', {
                    duration: Toast.durations.SHORT,
                    position: Toast.positions.BOTTOM,
                    backgroundColor: appColors.red,
                });
                this.setState({ sendingRequest: false });
            }
        }
    };

    renderIconRows = () => {
        const iconsPerRow = 4;
        const rows = [];
        
        for (let i = 0; i < possibleIcons.length; i += iconsPerRow) {
            const rowIcons = possibleIcons.slice(i, i + iconsPerRow);
            
            rows.push(
                <View key={i} style={styles.iconRow}>
                    {rowIcons.map((icon) => {
                        const isSelected = icon.id === this.state.editAgentForm.selectedId;
                        return (
                            <TouchableOpacity
                                key={icon.id}
                                onPress={() => this.handleIconPress(icon.id)}
                                style={[
                                    styles.iconButton,
                                    {
                                        borderWidth: isSelected ? scale(2) : scale(1),
                                        borderColor: isSelected ? appColors.primary : appColors.gray,
                                    }
                                ]}
                            >
                                {icon.render(isSelected)}
                            </TouchableOpacity>
                        );
                    })}
                    {/* Fill remaining slots with empty views to maintain consistent spacing */}
                    {Array.from({ length: iconsPerRow - rowIcons.length }).map((_, index) => (
                        <View key={`empty-${i}-${index}`} style={styles.iconButton} />
                    ))}
                </View>
            );
        }
        
        return rows;
    };

    render() {
        let { sendingRequest, editAgentForm, loading } = this.state;
        const { addressEmpty, nameEmpty, phoneEmpty, typeOfBusinessEmpty } = this.state.validation;
        const { t } = this.props;

        if (loading) {
            return (
                <Container>
                    <View style={styles.loadingContainer}>
                        <ActivityIndicator size={'large'} color={appColors.primary} />
                        <Text style={styles.loadingText}>{t('agents.loading') || 'Loading agent...'}</Text>
                    </View>
                </Container>
            );
        }

        return (
            <Container isScrollable>
                {
                    sendingRequest
                    ?
                    <ActivityIndicator size={'large'} color={appColors.primary} />
                    :
                    <Animated.View
                        style={{
                        marginTop: scale(75),
                        padding: scale(15),
                        borderRadius: scale(5)
                        }}
                        entering={FadeInUp.delay(300).duration(700)}
                    >
                        <TextInput
                            onChangeText={(text) => this.onChangeName(text)}
                            keyboardType="name-phone-pad"
                            leading={props => <Foundation name="torso-business" {...props} />}
                            inputStyle={ nameEmpty ? {color: appColors.red, paddingTop:scale(10)} :{ color: appColors.black, paddingTop:scale(10)} }
                            color={appColors.black}
                            placeholder={t('agents.name')}
                            value={editAgentForm.name}/>
                            {
                                nameEmpty
                                ?
                                <Text style={styles.errorMessage}>{t('agents.nameValidationFailed')}</Text>
                                :
                                null
                            } 
                        <View
                            style={{
                            paddingVertical: scale(5)
                        }}/>
                        <TextInput
                            onChangeText={(text) => this.onChangeAddress(text)}
                            keyboardType="name-phone-pad"
                            leading={props => <Entypo name="address" {...props} />}
                            inputStyle={ addressEmpty ? {color: appColors.red, paddingTop:scale(10)} :{ color: appColors.black, paddingTop:scale(10)} }
                            color={appColors.black}
                            placeholder={t('agents.address')}
                            value={editAgentForm.address}/>
                            {
                                addressEmpty
                                ?
                                <Text style={styles.errorMessage}>{t('agent.addressValidationFailed')}</Text>
                                :
                                null
                            } 
                        <View
                            style={{
                            paddingVertical: scale(5)
                        }}/>
                        <TextInput
                            onChangeText={(text) => this.onChangePhone(text)}
                            keyboardType="phone-pad"
                            leading={props => <Entypo name="phone" {...props} />}
                            inputStyle={ phoneEmpty ? {color: appColors.red, paddingTop:scale(10)} :{ color: appColors.black, paddingTop:scale(10)} }
                            color={appColors.black}
                            placeholder={t('agents.phone')}
                            value={editAgentForm.phone}/>
                            {
                                phoneEmpty
                                ?
                                <Text style={styles.errorMessage}>{t('agent.phoneValidationFailed')}</Text>
                                :
                                null
                            } 
                        <View
                            style={{
                            paddingVertical: scale(5)
                        }}/>
                        <TextInput
                            onChangeText={(text) => this.onChangeTypeOfBusiness(text)}
                            keyboardType="name-phone-pad"
                            leading={props => <MaterialCommunityIcons name="bank-plus" {...props} />}
                            inputStyle={ typeOfBusinessEmpty ? {color: appColors.red, paddingTop:scale(10)} :{ color: appColors.black, paddingTop:scale(10)} }
                            color={appColors.black}
                            placeholder={t('agents.type')}
                            value={editAgentForm.typeOfBusiness}/>
                            {
                                typeOfBusinessEmpty
                                ?
                                <Text style={styles.errorMessage}>{t('agent.typeValidationFailed')}</Text>
                                :
                                null
                            } 
                        <View
                            style={{
                            paddingVertical: scale(5)
                        }}/>
                        <View style={styles.iconContainer}>
                            {this.renderIconRows()}
                        </View>

                        <View style={styles.socialLoginWrapper}>
                            <View>
                                <TouchableOpacity style={styles.button} onPress={() => this.onUpdate()}>                                
                                    <Text style={styles.btnTxt}>{t('agents.update') || 'Update'}</Text>
                                    <MaterialIcons name="edit" size={scale(20)} color={appColors.primary}/>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </Animated.View>
                }
            </Container>
        );
    }
}

export default withTranslation()(AgentsEdit);

const styles = StyleSheet.create({
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    loadingText: {
        marginTop: scale(10),
        fontSize: scale(16),
        color: appColors.black,
    },
    socialLoginWrapper: {
        alignSelf: 'stretch',
    },
    button: {
        flexDirection: 'row',
        padding: scale(10),
        borderColor: appColors.gray,
        borderRadius: scale(25),
        borderWidth: StyleSheet.hairlineWidth,
        alignItems: 'center',
        justifyContent: 'center',
        gap: scale(5),
        marginBottom: scale(15),
    },
    btnTxt: {
        fontSize: scale(14),
        fontWeight: '600',
        color: appColors.black,
    },
    errorMessage: {
        color: appColors.redOrange
    },
    iconContainer: {
        marginVertical: scale(10),
    },
    iconRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: scale(12),
    },
    iconButton: {
        flex: 1,
        aspectRatio: 1,
        marginHorizontal: scale(4),
        padding: scale(10),
        borderRadius: scale(12),
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: appColors.white,
    }
});
