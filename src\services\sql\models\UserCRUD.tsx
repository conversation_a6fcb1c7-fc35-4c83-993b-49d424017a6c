import * as SQLite from 'expo-sqlite';

class UserCRUD {
    private db: SQLite.SQLiteDatabase | null = null;

    constructor() {
        this.initDB();
    }

    async initDB() : Promise < void > {
        this.db = await SQLite.openDatabaseAsync('Reports.db');
    }

    // Create: Insert a new user
    async createUser(name: string, email: string, password: string): Promise<void> {
        if (!this.db) {
            throw new Error('Database not initialized');
        }
        try {
            await this.db.runAsync(
                `INSERT INTO users (name, email, password) VALUES (?, ?, ?);`,
                name, email
            );
        } catch (error) {
            throw error;
        }
    }

    // Read: Get all users
    async getUsers(): Promise<{id: number; name: string; email: string}[]> {
        if (!this.db) {
            throw new Error('Database not initialized');
        }
        try {
            const result = await this.db.getAllAsync(`SELECT * FROM users;`);
            return result as {id: number; name: string; email: string}[];
        } catch (error) {
            throw error;
        }
    }

    // Update: Update user by id
    async updateUser(id: number, name: string, email: string): Promise<void> {
        if (!this.db) {
            throw new Error('Database not initialized');
        }
        try {
            await this.db.runAsync(
                `UPDATE users SET name = ?, email = ? WHERE id = ?;`,
                name, email, id
            );
        } catch (error) {
            throw error;
        }
    }

    // Delete: Delete user by id
    async deleteUser(id: number): Promise<void> {
        if (!this.db) {
            throw new Error('Database not initialized');
        }
        try {
            await this.db.runAsync(
                `DELETE FROM users WHERE id = ?;`,
                id
            );
        } catch (error) {
            throw error;
        }
    }
}

export default new UserCRUD();