import { MaterialCommunityIcons } from '@expo/vector-icons';
import React, { Component } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  ListRenderItemInfo,
  Pressable,
  RefreshControl,
} from 'react-native';
import { ActivityIndicator } from '@react-native-material/core';
import { appColors } from '../../utils/appColors';
import { scale, verticalScale, moderateScale } from 'react-native-size-matters';
import {withTranslation, WithTranslation} from 'react-i18next';
import AgentCRUD, { Agent } from '../../services/sql/models/AgentsCRUD';
import { possibleIcons } from './possibleIcons';


interface AgentListState {
  agents: Agent[];
  loading: boolean;
  refreshing: boolean;
}

interface AgentListProps extends WithTranslation {
  navigation: any;
}

class AgentList extends Component<AgentListProps, AgentListState> {
  constructor(props: AgentListProps) {
    super(props);
    this.state = {
      agents: [],
      loading: true,
      refreshing: false,
    };
  }

  componentDidMount() {
    this.loadAgents = this.loadAgents.bind(this);
    this.loadAgents();
    this.props.navigation.addListener('focus', this.loadAgents);
  }

  loadAgents = async () => {
    try {
      this.setState({ loading: true });
      const agents = await AgentCRUD.getAllAgents();
      this.setState({ agents, loading: false });
    } catch (error) {
      console.error('Error loading agents:', error);
      this.setState({ loading: false });
    }
  };

  onRefresh = async () => {
    try {
      this.setState({ refreshing: true });
      const agents = await AgentCRUD.getAllAgents();
      this.setState({ agents, refreshing: false });
    } catch (error) {
      console.error('Error refreshing agents:', error);
      this.setState({ refreshing: false });
    }
  };

  getIconComponentFromId = (iconId: number, isSelected: boolean = false) => {
    const icon = possibleIcons.find(icon => icon.id === iconId);
    return icon ? icon.render(isSelected) : (
      <MaterialCommunityIcons name="account-circle" size={scale(50)} color={appColors.black} />
    );
  };

  private renderAgent = ({ item }: ListRenderItemInfo<Agent>) => {
    return (
      <Pressable style={styles.card} onPress={() => this.navigateToEdit(item)}>
        <View style={styles.iconWrapper}>
          {this.getIconComponentFromId(item.iconId)}
        </View>
        <View style={styles.info}>
          <Text style={styles.name}>{item.name}</Text>
          <Text style={styles.details}>{item.typeOfBusiness}</Text>
          <Text style={styles.details}>
            {item.address}
          </Text>
          <Text style={styles.sports}>{item.phone}</Text>

        </View>
      </Pressable>
    );
  };

  navigateToEdit = (agent: Agent) => {
    this.props.navigation.navigate('AgentsEdit', { agentId: agent.id });
  };

  render() {
    const { agents, loading, refreshing } = this.state;

    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={appColors.primary} />
        </View>
      );
    }

    return (
      <FlatList
        data={agents}
        renderItem={this.renderAgent}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.container}
        numColumns={1}
        style={{flex: 1}}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={this.onRefresh}
            colors={[appColors.primary]}
          />
        }
      />
    );
  }
}

export default withTranslation()(AgentList);

const styles = StyleSheet.create({
  container: {
    padding: scale(10),
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  card: {
    flexDirection: 'row',
    backgroundColor: '#fbfbfb',
    borderWidth: scale(4),
    borderColor: '#DCDCDC',
    padding: scale(10),
    borderRadius: scale(10),
    marginBottom: verticalScale(10),
    elevation: scale(2),
  },
  iconWrapper: {
    width: scale(60),
    height: scale(60),
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: appColors.white,
    borderRadius: scale(8),
    marginRight: scale(10),
  },
  info: {
    flex: 1,
    justifyContent: 'center',
  },
  name: {
    fontSize: moderateScale(18),
    fontWeight: 'bold',
    color: appColors.black,
  },
  details: {
    fontSize: moderateScale(14),
    color: '#888',
    marginVertical: verticalScale(2),
  },
  sports: {
    fontSize: moderateScale(14),
    color: appColors.primary,
    fontWeight: '600',
    marginVertical: verticalScale(2),
  },
});
