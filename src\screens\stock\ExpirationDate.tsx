import React, { Component } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { scale, verticalScale, moderateScale } from 'react-native-size-matters';
import { appColors } from '../../utils/appColors';
import Container from '../../components/Container';
import { withTranslation, WithTranslation } from 'react-i18next';

interface ExpirationDateProps extends WithTranslation {
    navigation: any;
}

interface ExpirationDateState {}

class ExpirationDate extends Component<ExpirationDateProps, ExpirationDateState> {
    constructor(props: ExpirationDateProps) {
        super(props);
        this.state = {};
    }

    render() {
        const { t } = this.props;
        
        return (
            <Container>
                <View style={styles.container}>
                    <View style={styles.content}>
                        <Text style={styles.title}>Hello World!</Text>
                        <Text style={styles.subtitle}>
                            {t('dummy.welcome') || 'Welcome to the dummy page'}
                        </Text>
                        <Text style={styles.description}>
                            This is a placeholder page that can be customized with your content.
                        </Text>
                    </View>
                </View>
            </Container>
        );
    }
}

export default withTranslation()(ExpirationDate);

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: scale(20),
    },
    content: {
        alignItems: 'center',
        justifyContent: 'center',
    },
    title: {
        fontSize: moderateScale(32),
        fontWeight: 'bold',
        color: appColors.primary,
        marginBottom: verticalScale(20),
        textAlign: 'center',
    },
    subtitle: {
        fontSize: moderateScale(18),
        color: appColors.black,
        marginBottom: verticalScale(15),
        textAlign: 'center',
    },
    description: {
        fontSize: moderateScale(14),
        color: appColors.gray,
        textAlign: 'center',
        lineHeight: moderateScale(20),
        paddingHorizontal: scale(20),
    },
});
