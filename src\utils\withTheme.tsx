import React, { Component } from 'react';
import { ThemeColors, ThemeMode, useTheme } from './ThemeContext';

export interface WithThemeProps {
    theme: ThemeColors;
    themeMode: ThemeMode;
    toggleTheme: () => void;
    setTheme: (mode: ThemeMode) => void;
}

// HOC for class components to access theme
export function withTheme<P extends WithThemeProps>(
    WrappedComponent: React.ComponentType<P>
): React.ComponentType<Omit<P, keyof WithThemeProps>> {
    const WithThemeComponent = (props: Omit<P, keyof WithThemeProps>) => {
        const themeContext = useTheme();
        
        return (
            <WrappedComponent
                {...(props as P)}
                theme={themeContext.theme}
                themeMode={themeContext.themeMode}
                toggleTheme={themeContext.toggleTheme}
                setTheme={themeContext.setTheme}
            />
        );
    };

    WithThemeComponent.displayName = `withTheme(${WrappedComponent.displayName || WrappedComponent.name})`;
    
    return WithThemeComponent;
}
