import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { NavigationContainer } from '@react-navigation/native';
import { Pressable } from 'react-native-gesture-handler';
import { AntDesign, Feather, Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';

import Login from '../screens/auth/Login';

import Welcome from '../screens/Welcome';

import { appColors } from '../utils/appColors';

import { scale } from 'react-native-size-matters';
import AgentList from '../screens/agents/AgentList';
import Register from '../screens/auth/Register';
import AgentNew from '../screens/agents/AgentNew';
import { renderBold } from '../services/FontProvider';
import Leftovers from '../screens/stock/Leftovers';
import { MyTabsNew } from './Tab';
import PersonalDetails from '../screens/personal/PersonalDetails';
import CategoryReports from '../screens/reports/CategoryReports';
import TopSales from '../screens/reports/TopSales';
import NullSales from '../screens/reports/NullSales';
import { AverageInvoice } from '../screens/reports/AverageInvoice';
import { CompareSales } from '../screens/reports/CompareSales';
import { Daily } from '../screens/reports/Daily';
import { MarginSales } from '../screens/reports/MarginSales';
import AgentsEdit from '../screens/agents/AgentsEdit';
import CriticallyLow from '../screens/stock/CriticallyLow';
import ExpirationDate from '../screens/stock/ExpirationDate';


const Stack = createStackNavigator();


export default function MyStack() {
  const { t } = useTranslation();
  return (
    <NavigationContainer>
      <Stack.Navigator initialRouteName="MyTabsNew">
      <Stack.Screen
          name="MyTabsNew"
          component={MyTabsNew}
          options={{ headerShown: false, headerLeft: () => null }}
        />
        <Stack.Screen
          name="Welcome"
          component={Welcome}
          options={{
            headerShown: false,
            headerLeft: () => null,
          }}
        />

        <Stack.Screen
          name="Login"
          component={Login}
          options={({ navigation }) => ({
            headerTitle: t('login.login'),
            headerTitleStyle: { fontSize: scale(24), fontFamily: renderBold() },
            presentation: 'modal',
            headerShadowVisible: false,
            headerStyle: {
              backgroundColor: appColors.white
            },
            headerLeft: () => (
              <Pressable onPress={() => navigation.goBack()}>
                <Feather name="chevron-down" size={45} color={appColors.black} />
              </Pressable>
            ),
          })}
        />
        <Stack.Screen
          name="Register"
          component={Register}
          options={({ navigation }) => ({
            headerTitle: t('register.register'),
            headerTitleStyle: { fontSize: scale(24), fontFamily: renderBold()  },
            headerStyle: {
              backgroundColor:  appColors.white
            },
            headerShadowVisible: false,
            presentation: 'modal',
            headerLeft: () => (
              <Pressable onPress={() => navigation.goBack()}>
                <Feather name="chevron-down" size={45} color={appColors.black} />
              </Pressable>
            ),
          })}
        />

      <Stack.Screen 
          name="AgentNew" 
          component={AgentNew}
          options={({ navigation }) => ({
            headerTitle:  t('agents.newAgent'),
            headerTitleStyle: { fontSize: scale(24), fontFamily: renderBold() },
            headerShadowVisible: false,
            headerStyle: {
              backgroundColor: 'transparent'
            },
            headerLeft: () => (
              <Pressable onPress={() => navigation.goBack()} style={{ marginLeft: scale(10) }}>
                <AntDesign name="arrowleft" size={35} color={appColors.black} />
              </Pressable>
            ),
          })}
        />

        <Stack.Screen 
          name="AgentsEdit" 
          component={AgentsEdit}
          options={({ navigation }) => ({
            headerTitle:  'Edit agents', //t('agents.newAgent'),
            headerTitleStyle: { fontSize: scale(24), fontFamily: renderBold() },
            headerShadowVisible: false,
            headerStyle: {
              backgroundColor: 'transparent'
            },
            headerLeft: () => (
              <Pressable onPress={() => navigation.goBack()} style={{ marginLeft: scale(10) }}>
                <AntDesign name="arrowleft" size={35} color={appColors.black} />
              </Pressable>
            ),
          })}
        />

      <Stack.Screen name="Leftovers" component={Leftovers}
          options={({ navigation }) => ({
            headerTitle:  t('leftovers.title'),
            headerTitleStyle: { fontSize: scale(24), fontFamily: renderBold() },
            headerShadowVisible: false,
            headerStyle: {
              backgroundColor: 'transparent'
            },
            headerLeft: () => (
              <Pressable onPress={() => navigation.goBack()} style={{ marginLeft: scale(10) }}>
                <AntDesign name="arrowleft" size={35} color={appColors.black} />
              </Pressable>
            ),
          })}
        />

        <Stack.Screen name="LowStock" component={CriticallyLow}
          options={({ navigation }) => ({
            headerTitle:  t('leftovers.title'),
            headerTitleStyle: { fontSize: scale(24), fontFamily: renderBold() },
            headerShadowVisible: false,
            headerStyle: {
              backgroundColor: 'transparent'
            },
            headerLeft: () => (
              <Pressable onPress={() => navigation.goBack()} style={{ marginLeft: scale(10) }}>
                <AntDesign name="arrowleft" size={35} color={appColors.black} />
              </Pressable>
            ),
          })}
        />

        <Stack.Screen name="ExpirationDate" component={ExpirationDate}
          options={({ navigation }) => ({
            headerTitle:  t('leftovers.title'),
            headerTitleStyle: { fontSize: scale(24), fontFamily: renderBold() },
            headerShadowVisible: false,
            headerStyle: {
              backgroundColor: 'transparent'
            },
            headerLeft: () => (
              <Pressable onPress={() => navigation.goBack()} style={{ marginLeft: scale(10) }}>
                <AntDesign name="arrowleft" size={35} color={appColors.black} />
              </Pressable>
            ),
          })}
        />


        <Stack.Screen name="CategoryReports" component={CategoryReports}
          options={({ navigation }) => ({
            headerTitle: 'Category Reports',
            headerTitleStyle: { fontSize: scale(18), fontFamily: renderBold() },
            headerShadowVisible: false,
            headerStyle: {
              backgroundColor: 'transparent'
            },
            headerLeft: () => (
              <Pressable onPress={() => navigation.goBack()} style={{ marginLeft: 10 }}>
                <AntDesign name="arrowleft" size={35} color={appColors.black} />
              </Pressable>
            ),
            headerRight: () => (
              <Pressable onPress={() => navigation.navigate('Login')}>
                <Ionicons name="log-out-outline" size={35} color={appColors.black} />
              </Pressable>
            ),
          })}
        />

        <Stack.Screen name="TopSales" component={TopSales}
          options={({ navigation }) => ({
            headerTitle: 'Top Sales',
            headerTitleStyle: { fontSize: scale(18), fontFamily: renderBold() },
            headerShadowVisible: false,
            headerStyle: {
              backgroundColor: 'transparent'
            },
            headerLeft: () => (
              <Pressable onPress={() => navigation.goBack()} style={{ marginLeft: 10 }}>
                <AntDesign name="arrowleft" size={35} color={appColors.black} />
              </Pressable>
            ),
            headerRight: () => (
              <Pressable onPress={() => navigation.navigate('Login')}>
                <Ionicons name="log-out-outline" size={35} color={appColors.black} />
              </Pressable>
            ),
          })}
        />

        <Stack.Screen name="NullSales" component={NullSales}
          options={({ navigation }) => ({
            headerTitle:  'Null Sales',
            headerTitleStyle: { fontSize: scale(24), fontFamily: renderBold() },
            headerShadowVisible: false,
            headerStyle: {
              backgroundColor: 'transparent'
            },
            headerLeft: () => (
              <Pressable onPress={() => navigation.goBack()} style={{ marginLeft: scale(10) }}>
                <AntDesign name="arrowleft" size={35} color={appColors.black} />
              </Pressable>
            ),
          })}
        />

        <Stack.Screen name="Returns" component={NullSales}
          options={({ navigation }) => ({
            headerTitle:  'Returns',
            headerTitleStyle: { fontSize: scale(24), fontFamily: renderBold() },
            headerShadowVisible: false,
            headerStyle: {
              backgroundColor: 'transparent'
            },
            headerLeft: () => (
              <Pressable onPress={() => navigation.goBack()} style={{ marginLeft: scale(10) }}>
                <AntDesign name="arrowleft" size={35} color={appColors.black} />
              </Pressable>
            ),
          })}
        />

        <Stack.Screen name="AverageInvoice" component={AverageInvoice}
          options={({ navigation }) => ({
            headerTitle:  'Average Invoice',
            headerTitleStyle: { fontSize: scale(24), fontFamily: renderBold() },
            headerShadowVisible: false,
            headerStyle: {
              backgroundColor: 'transparent'
            },
            headerLeft: () => (
              <Pressable onPress={() => navigation.goBack()} style={{ marginLeft: scale(10) }}>
                <AntDesign name="arrowleft" size={35} color={appColors.black} />
              </Pressable>
            ),
          })}
        />

        <Stack.Screen name="CompareSales" component={CompareSales}
          options={({ navigation }) => ({
            headerTitle:  'Compare Sales',
            headerTitleStyle: { fontSize: scale(24), fontFamily: renderBold() },
            headerShadowVisible: false,
            headerStyle: {
              backgroundColor: 'transparent'
            },
            headerLeft: () => (
              <Pressable onPress={() => navigation.goBack()} style={{ marginLeft: scale(10) }}>
                <AntDesign name="arrowleft" size={35} color={appColors.black} />
              </Pressable>
            ),
          })}
        />

        <Stack.Screen name="Daily" component={Daily}
          options={({ navigation }) => ({
            headerTitle:  'Daily',
            headerTitleStyle: { fontSize: scale(24), fontFamily: renderBold() },
            headerShadowVisible: false,
            headerStyle: {
              backgroundColor: 'transparent'
            },
            headerLeft: () => (
              <Pressable onPress={() => navigation.goBack()} style={{ marginLeft: scale(10) }}>
                <AntDesign name="arrowleft" size={35} color={appColors.black} />
              </Pressable>
            ),
          })}
        />

        <Stack.Screen name="MarginSales" component={MarginSales}
          options={({ navigation }) => ({
            headerTitle:  'Margin Sales',
            headerTitleStyle: { fontSize: scale(24), fontFamily: renderBold() },
            headerShadowVisible: false,
            headerStyle: {
              backgroundColor: 'transparent'
            },
            headerLeft: () => (
              <Pressable onPress={() => navigation.goBack()} style={{ marginLeft: scale(10) }}>
                <AntDesign name="arrowleft" size={35} color={appColors.black} />
              </Pressable>
            ),
          })}
        />

        <Stack.Screen name="PersonalDetails" component={PersonalDetails}
          options={({ navigation }) => ({
            headerTitle: 'Personal Details',
            headerTitleStyle: { fontSize: scale(18), fontFamily: renderBold() },
            presentation: 'card',
            headerShadowVisible: false,
            headerStyle: {
              backgroundColor: 'transparent'
            },
            headerLeft: () => (
              <Pressable onPress={() => navigation.goBack()} style={{ marginLeft: 10 }}>
                <AntDesign name="arrowleft" size={35} color={appColors.black} />
              </Pressable>
            ),
          })}
        />  
      </Stack.Navigator>
    </NavigationContainer>
  );
}
