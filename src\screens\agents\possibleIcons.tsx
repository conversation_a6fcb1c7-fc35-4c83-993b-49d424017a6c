// icons/possibleIcons.ts
import FontAwesome from "react-native-vector-icons/FontAwesome";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";

const scale = (size: number) => size; // adjust with your real scale util

export type IconDef = {
  id: number;
  render: (isSelected: boolean) => JSX.Element;
};

export const possibleIcons: IconDef[] = [
  {
    id: 1,
    render: (isSelected) => (
      <FontAwesome
        name="building-o"
        size={scale(24)}
        color={isSelected ? "blue" : "black"}
      />
    ),
  },
  {
    id: 2,
    render: (isSelected) => (
      <FontAwesome
        name="hospital-o"
        size={scale(24)}
        color={isSelected ? "blue" : "black"}
      />
    ),
  },
  {
    id: 3,
    render: (isSelected) => (
      <FontAwesome
        name="bank"
        size={scale(24)}
        color={isSelected ? "blue" : "black"}
      />
    ),
  },
  {
    id: 4,
    render: (isSelected) => (
      <MaterialCommunityIcons
        name="hospital-building"
        size={scale(24)}
        color={isSelected ? "blue" : "black"}
      />
    ),
  },
  {
    id: 5,
    render: (isSelected) => (
      <MaterialCommunityIcons
        name="office-building"
        size={scale(24)}
        color={isSelected ? "blue" : "black"}
      />
    ),
  },
  {
    id: 6,
    render: (isSelected) => (
      <MaterialCommunityIcons
        name="shopping"
        size={scale(24)}
        color={isSelected ? "blue" : "black"}
      />
    ),
  },
  {
    id: 7,
    render: (isSelected) => (
      <MaterialCommunityIcons
        name="bank"
        size={scale(24)}
        color={isSelected ? "blue" : "black"}
      />
    ),
  },
  {
    id: 8,
    render: (isSelected) => (
      <MaterialCommunityIcons
        name="human-male"
        size={scale(24)}
        color={isSelected ? "blue" : "black"}
      />
    ),
  },
];
