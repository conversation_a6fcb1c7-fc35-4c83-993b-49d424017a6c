class UserService {

    public login = async ({name,email,password}) : Promise < any > => {
        return new Promise((resolve, reject) => {
            resolve({
                id: 1,
                name: name,
                email: email,
                password: password
            });
        });
    }

    public register = async ({name,email,password}) : Promise < any > => {
        return new Promise((resolve, reject) => {
            resolve({
                id: 1,
                name: name,
                email: email,
                password: password
            });
        });
    }
}

export default new UserService();