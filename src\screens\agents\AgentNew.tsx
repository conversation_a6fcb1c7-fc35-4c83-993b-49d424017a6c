import React, {Component} from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {TextInput, ActivityIndicator} from '@react-native-material/core';
import {scale} from 'react-native-size-matters';
import Container from '../../components/Container';
import {appColors} from '../../utils/appColors';
import Animated, {FadeInUp} from 'react-native-reanimated';
import {Entypo, Foundation, MaterialCommunityIcons, MaterialIcons} from '@expo/vector-icons';
import {withTranslation, WithTranslation} from 'react-i18next';
import Toast from 'react-native-root-toast';
import { possibleIcons } from './possibleIcons';
import AgentCRUD from '../../services/sql/models/AgentsCRUD';
import { withTheme, WithThemeProps } from '../../utils/withTheme';

interface AgentInputValidation {
    nameEmpty: boolean;
    addressEmpty: boolean;
    phoneEmpty:boolean;
    typeOfBusinessEmpty:boolean;
}

interface AgentNewForm{
    name:string;
    address:string;
    phone:string;
    typeOfBusiness:string;
    selectedId:number | null;
}

interface AgentNewProps extends WithTranslation, WithThemeProps {
    navigation: any;
}

interface AgentNewState {
    validation: AgentInputValidation;
    newAgentForm: AgentNewForm;
    sendingRequest: boolean;
}

class AgentNew extends Component<AgentNewProps, AgentNewState> {
    constructor(props: AgentNewProps) {
        super(props);
        this.state = {
            validation:{
                nameEmpty: false,
                addressEmpty: false,
                phoneEmpty: false,
                typeOfBusinessEmpty: false,
            },
            newAgentForm:{
                name:'',
                address:'',
                phone:'',
                typeOfBusiness:'',
                selectedId:1
            },
            sendingRequest: false
        };
    }

    onChangeName = (text: string) => {
        this.setState({
            newAgentForm: { ...this.state.newAgentForm, name: text },
            validation: { ...this.state.validation, nameEmpty: false }
        });
    };

    onChangeAddress = (text: string) => {
        this.setState({
            newAgentForm: { ...this.state.newAgentForm, address: text },
            validation: { ...this.state.validation, addressEmpty: false }
        });
    };

    onChangePhone = (text: string) => {
        this.setState({
            newAgentForm: { ...this.state.newAgentForm, phone: text },
            validation: { ...this.state.validation, phoneEmpty: false }
        });
    };

    onChangeTypeOfBusiness = (text: string) => {
        this.setState({
            newAgentForm: { ...this.state.newAgentForm, typeOfBusiness: text },
            validation: { ...this.state.validation, typeOfBusinessEmpty: false }
        });
    };

    handleIconPress = (id: number) => {
        this.setState({
            newAgentForm: { ...this.state.newAgentForm, selectedId: id }
        });
    };

    renderIconRows = (theme: any, styles: any) => {
        const iconsPerRow = 4;
        const rows = [];

        for (let i = 0; i < possibleIcons.length; i += iconsPerRow) {
            const rowIcons = possibleIcons.slice(i, i + iconsPerRow);

            rows.push(
                <View key={i} style={styles.iconRow}>
                    {rowIcons.map((icon) => {
                        const isSelected = icon.id === this.state.newAgentForm.selectedId;
                        return (
                            <TouchableOpacity
                                key={icon.id}
                                onPress={() => this.handleIconPress(icon.id)}
                                style={[
                                    styles.iconButton,
                                    {
                                        borderWidth: isSelected ? scale(2) : scale(1),
                                        borderColor: isSelected ? theme.primary : theme.border,
                                    }
                                ]}
                            >
                                {icon.render(isSelected)}
                            </TouchableOpacity>
                        );
                    })}
                    {/* Fill remaining slots with empty views to maintain consistent spacing */}
                    {Array.from({ length: iconsPerRow - rowIcons.length }).map((_, index) => (
                        <View key={`empty-${i}-${index}`} style={styles.iconButton} />
                    ))}
                </View>
            );
        }

        return rows;
    };

    validateInput = () => {
        let { validation } = this.state;
        const { name, address, phone, typeOfBusiness } = this.state.newAgentForm;
        let validationPassed = true;

        // Reset validation state
        validation = {
            nameEmpty: false,
            addressEmpty: false,
            phoneEmpty: false,
            typeOfBusinessEmpty: false,
        };

        if (!name.trim()) {
            validation.nameEmpty = true;
            validationPassed = false;
        }

        if (!address.trim()) {
            validation.addressEmpty = true;
            validationPassed = false;
        }

        if (!phone.trim()) {
            validation.phoneEmpty = true;
            validationPassed = false;
        }

        if (!typeOfBusiness.trim()) {
            validation.typeOfBusinessEmpty = true;
            validationPassed = false;
        }

        this.setState({ validation });
        return validationPassed;
    }

    onSignUp = async () => {
        if (this.validateInput()) {
            const { newAgentForm } = this.state;
            const { t, theme } = this.props;

            // Check if selectedId is valid
            if (!newAgentForm.selectedId) {
                Toast.show(t('agents.selectIconError') || 'Please select an icon', {
                    duration: Toast.durations.SHORT,
                    position: Toast.positions.BOTTOM,
                    backgroundColor: theme.error,
                });
                return;
            }

            this.setState({ sendingRequest: true });

            try {
                // Check if agent already exists
                const agentExists = await AgentCRUD.agentExists(newAgentForm.name, newAgentForm.phone);

                if (agentExists) {
                    Toast.show(t('agents.agentExistsError') || 'Agent with this name and phone already exists', {
                        duration: Toast.durations.SHORT,
                        position: Toast.positions.BOTTOM,
                        backgroundColor: theme.error,
                    });
                    this.setState({ sendingRequest: false });
                    return;
                }

                // Create new agent
                const agentId = await AgentCRUD.createAgent({
                    name: newAgentForm.name,
                    address: newAgentForm.address,
                    phone: newAgentForm.phone,
                    typeOfBusiness: newAgentForm.typeOfBusiness,
                    iconId: newAgentForm.selectedId
                });

                // Show success message
                Toast.show(t('agents.saveSuccess') || 'Agent saved successfully', {
                    duration: Toast.durations.SHORT,
                    position: Toast.positions.BOTTOM,
                    backgroundColor: theme.success,
                });

                // Reset form
                this.setState({
                    newAgentForm: {
                        name: '',
                        address: '',
                        phone: '',
                        typeOfBusiness: '',
                        selectedId: 1
                    },
                    validation: {
                        nameEmpty: false,
                        addressEmpty: false,
                        phoneEmpty: false,
                        typeOfBusinessEmpty: false,
                    },
                    sendingRequest: false
                });

                // Navigate back or to agents list
                this.props.navigation.goBack();

            } catch (error) {
                console.error('Error saving agent:', error);
                Toast.show(t('agents.saveError') || 'Error saving agent', {
                    duration: Toast.durations.SHORT,
                    position: Toast.positions.BOTTOM,
                    backgroundColor: theme.error,
                });
                this.setState({ sendingRequest: false });
            }
        }
    };


    render() {
        let { sendingRequest, newAgentForm, } = this.state;
        const { addressEmpty, nameEmpty, phoneEmpty, typeOfBusinessEmpty } = this.state.validation;
        const { t, theme } = this.props;
        const styles = createStyles(theme);
        return (
            <Container isScrollable>
                {
                    sendingRequest
                    ?
                    <ActivityIndicator size={'large'} color={theme.primary} />
                    :
                    <Animated.View
                        style={{
                        marginTop: scale(75),
                        padding: scale(15),
                        borderRadius: scale(5)
                        }}
                        entering={FadeInUp.delay(300).duration(700)}
                    >
                        <TextInput
                            onChangeText={(text) => this.onChangeName(text)}
                            keyboardType="name-phone-pad"
                            leading={props => <Foundation name="torso-business" {...props} />}
                            inputStyle={ nameEmpty ? {color: theme.error, paddingTop:scale(10)} :{ color: theme.text, paddingTop:scale(10)} }
                            color={theme.text}
                            placeholder={t('agents.name')}
                            value={newAgentForm.name}/>
                            {
                                nameEmpty
                                ?
                                <Text style={styles.errorMessage}>{t('agents.nameValidationFailed')}</Text>
                                :
                                null
                            } 
                        <View
                            style={{
                            paddingVertical: scale(5)
                        }}/>
                        <TextInput
                            onChangeText={(text) => this.onChangeAddress(text)}
                            keyboardType="name-phone-pad"
                            leading={props => <Entypo name="address" {...props} />}
                            inputStyle={ addressEmpty ? {color: theme.error, paddingTop:scale(10)} :{ color: theme.text, paddingTop:scale(10)} }
                            color={theme.text}
                            placeholder={t('agents.address')}
                            value={newAgentForm.address}/>
                            {
                                addressEmpty
                                ?
                                <Text style={styles.errorMessage}>{t('agent.addressValidationFailed')}</Text>
                                :
                                null
                            } 
                        <View
                            style={{
                            paddingVertical: scale(5)
                        }}/>
                        <TextInput
                            onChangeText={(text) => this.onChangePhone(text)}
                            keyboardType="phone-pad"
                            leading={props => <Entypo name="phone" {...props} />}
                            inputStyle={ phoneEmpty ? {color: theme.error, paddingTop:scale(10)} :{ color: theme.text, paddingTop:scale(10)} }
                            color={theme.text}
                            placeholder={t('agents.phone')}
                            value={newAgentForm.phone}/>
                            {
                                phoneEmpty
                                ?
                                <Text style={styles.errorMessage}>{t('agent.phoneValidationFailed')}</Text>
                                :
                                null
                            } 
                        <View
                            style={{
                            paddingVertical: scale(5)
                        }}/>
                        <TextInput
                            onChangeText={(text) => this.onChangeTypeOfBusiness(text)}
                            keyboardType="name-phone-pad"
                            leading={props => <MaterialCommunityIcons name="bank-plus" {...props} />}
                            inputStyle={ typeOfBusinessEmpty ? {color: theme.error, paddingTop:scale(10)} :{ color: theme.text, paddingTop:scale(10)} }
                            color={theme.text}
                            placeholder={t('agents.type')}
                            value={newAgentForm.typeOfBusiness}/>
                            {
                                typeOfBusinessEmpty
                                ?
                                <Text style={styles.errorMessage}>{t('agent.typeValidationFailed')}</Text>
                                :
                                null
                            } 
                        <View
                            style={{
                            paddingVertical: scale(5)
                        }}/>
                        <View style={styles.iconContainer}>
                            {this.renderIconRows(theme, styles)}
                        </View>


                        <View style={styles.socialLoginWrapper}>
                            <View>
                                <TouchableOpacity style={styles.button} onPress={() => this.onSignUp()}>                                
                                    <Text style={styles.btnTxt}>{t('agents.save')}</Text>
                                    <MaterialIcons name="add-business" size={scale(20)} color={theme.primary}/>
                                </TouchableOpacity>
                            </View>
                        </View>
                      
                        
                    </Animated.View>
                }
            </Container>
        );
    }
}

export default withTheme(withTranslation()(AgentNew));

const createStyles = (theme: any) => StyleSheet.create({
    socialLoginWrapper: {
        alignSelf: 'stretch',
    },
    button: {
        flexDirection: 'row',
        padding: scale(10),
        borderColor: theme.border,
        borderRadius: scale(25),
        borderWidth: StyleSheet.hairlineWidth,
        alignItems: 'center',
        justifyContent: 'center',
        gap: scale(5),
        marginBottom: scale(15),
        backgroundColor: theme.surface,
    },
    btnTxt: {
        fontSize: scale(14),
        fontWeight: '600',
        color: theme.text,
    },
    errorMessage: {
        color: theme.error
    },
    iconContainer: {
        marginVertical: scale(10),
    },
    iconRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: scale(12),
    },
    iconButton: {
        flex: 1,
        aspectRatio: 1,
        marginHorizontal: scale(4),
        padding: scale(10),
        borderRadius: scale(12),
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: theme.surface,
    }
})
