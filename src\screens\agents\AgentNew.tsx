import React, {Component} from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {TextInput, ActivityIndicator} from '@react-native-material/core';
import {scale} from 'react-native-size-matters';
import Container from '../../components/Container';
import {appColors} from '../../utils/appColors';
import Animated, {FadeInUp} from 'react-native-reanimated';
import {Entypo, Foundation, MaterialCommunityIcons, MaterialIcons} from '@expo/vector-icons';
import {withTranslation, WithTranslation} from 'react-i18next';
import { possibleIcons } from './possibleIcons';

interface AgentInputValidation {
    nameEmpty: boolean;
    addressEmpty: boolean;
    phoneEmpty:boolean;
    typeOfBusinessEmpty:boolean;
}

interface AgentNewForm{
    name:string;
    address:string;
    phone:string;
    typeOfBusiness:string;
    selectedId:number | null;
}

interface LoginProps extends WithTranslation {
    navigation : any;
}

interface LoginState {
    validation:AgentInputValidation;
    newAgentForm:AgentNewForm;
    sendingRequest: boolean;
}

class AgentNew extends Component < LoginProps, LoginState > {
    constructor(props : LoginProps) {
        super(props);
        this.state = {
            validation:{
                nameEmpty: false,
                addressEmpty: false,
                phoneEmpty: false,
                typeOfBusinessEmpty: false,
            },
            newAgentForm:{
                name:'',
                address:'',
                phone:'',
                typeOfBusiness:'',
                selectedId:0
            },
            sendingRequest: false
        };
    }

    onChangeName = (text: string) => {
        this.setState({
            newAgentForm: { ...this.state.newAgentForm, name: text },
            validation: { ...this.state.validation, nameEmpty: false }
        });
    };

    onChangeAddress = (text: string) => {
        this.setState({
            newAgentForm: { ...this.state.newAgentForm, address: text },
            validation: { ...this.state.validation, addressEmpty: false }
        });
    };

    onChangePhone = (text: string) => {
        this.setState({
            newAgentForm: { ...this.state.newAgentForm, phone: text },
            validation: { ...this.state.validation, phoneEmpty: false }
        });
    };

    onChangeTypeOfBusiness = (text: string) => {
        this.setState({
            newAgentForm: { ...this.state.newAgentForm, typeOfBusiness: text },
            validation: { ...this.state.validation, typeOfBusinessEmpty: false }
        });
    };

    handleIconPress = (id: number) => {
        this.setState({
            newAgentForm: { ...this.state.newAgentForm, selectedId: id }
        });
    };

    validateInput = () => {
        let { validation } = this.state;
        const { name, address, phone, typeOfBusiness    } = this.state.newAgentForm;
        let validationPassed = true;
        if (!name ) {
            validation.nameEmpty = true;
            this.setState({validation : validation})
            validationPassed = false;
        }

        if (!address) {
            validation.addressEmpty = true;
            this.setState({validation : validation})
            validationPassed = false;
        }   

        if (!phone ) {
            validation.phoneEmpty = true;
            this.setState({validation : validation})
            validationPassed = false;
        }

        if (!typeOfBusiness) {
            validation.typeOfBusinessEmpty = true;
            this.setState({validation : validation})
            validationPassed = false;
        }   
    
        return validationPassed;
    }

    onSignUp = () => {
        if (this.validateInput()) {
            // TODO: Implement agent creation logic
            console.log('Agent form data:', this.state.newAgentForm);
        }
    };


    render() {
        let { sendingRequest, newAgentForm, } = this.state;
        const { addressEmpty, nameEmpty, phoneEmpty, typeOfBusinessEmpty } = this.state.validation;
        const { t } = this.props;     
        return (
            <Container isScrollable>
                {
                    sendingRequest
                    ?
                    <ActivityIndicator size={'large'} color={appColors.primary} />
                    :
                    <Animated.View
                        style={{
                        marginTop: scale(75),
                        padding: scale(15),
                        borderRadius: scale(5)
                        }}
                        entering={FadeInUp.delay(300).duration(700)}
                    >
                        <TextInput
                            onChangeText={(text) => this.onChangeName(text)}
                            keyboardType="name-phone-pad"
                            leading={props => <Foundation name="torso-business" {...props} />}
                            inputStyle={ nameEmpty ? {color: appColors.red, paddingTop:scale(10)} :{ color: appColors.black, paddingTop:scale(10)} }
                            color={appColors.black}
                            placeholder={t('agents.name')}
                            value={newAgentForm.name}/>
                            {
                                nameEmpty
                                ?
                                <Text style={styles.errorMessage}>{t('agents.nameValidationFailed')}</Text>
                                :
                                null
                            } 
                        <View
                            style={{
                            paddingVertical: scale(5)
                        }}/>
                        <TextInput
                            onChangeText={(text) => this.onChangeAddress(text)}
                            keyboardType="name-phone-pad"
                            leading={props => <Entypo name="address" {...props} />}
                            inputStyle={ addressEmpty ? {color: appColors.red, paddingTop:scale(10)} :{ color: appColors.black, paddingTop:scale(10)} }
                            color={appColors.black}
                            placeholder={t('agents.address')}
                            value={newAgentForm.address}/>
                            {
                                addressEmpty
                                ?
                                <Text style={styles.errorMessage}>{t('agent.addressValidationFailed')}</Text>
                                :
                                null
                            } 
                        <View
                            style={{
                            paddingVertical: scale(5)
                        }}/>
                        <TextInput
                            onChangeText={(text) => this.onChangePhone(text)}
                            keyboardType="phone-pad"
                            leading={props => <Entypo name="phone" {...props} />}
                            inputStyle={ phoneEmpty ? {color: appColors.red, paddingTop:scale(10)} :{ color: appColors.black, paddingTop:scale(10)} }
                            color={appColors.black}
                            placeholder={t('agents.phone')}
                            value={newAgentForm.phone}/>
                            {
                                phoneEmpty
                                ?
                                <Text style={styles.errorMessage}>{t('agent.phoneValidationFailed')}</Text>
                                :
                                null
                            } 
                        <View
                            style={{
                            paddingVertical: scale(5)
                        }}/>
                        <TextInput
                            onChangeText={(text) => this.onChangeTypeOfBusiness(text)}
                            keyboardType="name-phone-pad"
                            leading={props => <MaterialCommunityIcons name="bank-plus" {...props} />}
                            inputStyle={ typeOfBusinessEmpty ? {color: appColors.red, paddingTop:scale(10)} :{ color: appColors.black, paddingTop:scale(10)} }
                            color={appColors.black}
                            placeholder={t('agents.type')}
                            value={newAgentForm.typeOfBusiness}/>
                            {
                                typeOfBusinessEmpty
                                ?
                                <Text style={styles.errorMessage}>{t('agent.typeValidationFailed')}</Text>
                                :
                                null
                            } 
                        <View
                            style={{
                            paddingVertical: scale(5)
                        }}/>
                       <View style={{ flexDirection: "row", flexWrap: "wrap", gap: 12 }}>
                            {possibleIcons.map((icon) => {
                            const isSelected = icon.id === newAgentForm.selectedId;
                            return (
                                <TouchableOpacity
                                key={icon.id}
                                onPress={() => this.handleIconPress(icon.id)}
                                style={{
                                    padding: 10,
                                    borderRadius: 12,
                                    borderWidth: isSelected ? 2 : 1,
                                    borderColor: isSelected ? "blue" : "gray",
                                }}
                                >
                                {icon.render(isSelected)}
                                </TouchableOpacity>
                            );
                            })}
                        </View>


                        <View style={styles.socialLoginWrapper}>
                            <View>
                                <TouchableOpacity style={styles.button} onPress={() => this.onSignUp()}>                                
                                    <Text style={styles.btnTxt}>{t('agents.save')}</Text>
                                    <MaterialIcons name="add-business" size={scale(20)} color={appColors.primary}/>
                                </TouchableOpacity>
                            </View>
                        </View>
                      
                        
                    </Animated.View>
                }
            </Container>
        );
    }
}

export default withTranslation()(AgentNew);

const styles = StyleSheet.create({
    socialLoginWrapper:{
        alignSelf:'stretch',
    },
    button:{
        flexDirection:'row',
        padding:scale(10),
        borderColor:appColors.gray,
        borderRadius:scale(25),
        borderWidth:StyleSheet.hairlineWidth,
        alignItems:'center',
        justifyContent:'center',
        gap:scale(5),
        marginBottom:scale(15),
    },
    btnTxt:{
        fontSize:scale(14),
        fontWeight:'600',
        color:appColors.black,
    },
    errorMessage:{
        color:appColors.redOrange
    }
})
